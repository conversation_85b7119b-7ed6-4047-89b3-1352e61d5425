<%# views/layouts/simple_layout.ejs %>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= locals.pageTitle || (locals.config && locals.config.app && locals.config.app.name) || 'Master-Map' %></title>
    <link rel="stylesheet" href="/css/user_layout.css"> 
    <!-- Font Awesome für Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" xintegrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <%# Platzhalter für seitenspezifische Stylesheets %>
    <%- defineContent('pageStylesheets') %>

</head>
<body class="simple-page-layout <%= typeof pageSpecificClass !== 'undefined' ? pageSpecificClass : 'default-simple-class' %>">

    <div class="simple-content-box">
        <%# Globale Nachrichten-Boxen (werden nur angezeigt, wenn die Variable gesetzt ist) %>
        <% if (locals.successMessage && locals.successMessage.length > 0) { %>
            <div class="message success-message">
                <% successMessage.forEach(function(msg) { %>
                    <p><%- msg %></p>
                <% }); %>
            </div>
        <% } %>
        <% if (locals.errorMessage && locals.errorMessage.length > 0) { %>
            <div class="message error-message">
                <% errorMessage.forEach(function(msg) { %>
                    <p><%- msg %></p>
                <% }); %>
            </div>
        <% } %>
        <% if (locals.infoMessage && locals.infoMessage.length > 0) { %>
            <div class="message info-message">
                <% infoMessage.forEach(function(msg) { %>
                    <p><%- msg %></p>
                <% }); %>
            </div>
        <% } %>

        <%- body %> <%# Hier wird der Inhalt von login.ejs oder error.ejs injiziert %>
    </div>

    <%# Platzhalter für seitenspezifische Skripte %>
    <%- defineContent('pageScripts') %>
</body>
</html>
