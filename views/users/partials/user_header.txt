<%# views/users/partials/user_header.ejs %>
<header class="user-main-header">
    <div class="user-header-content">
        <h1 class="user-header-title">
            <a href="/user/dashboard">
                <%= currentUser && currentUser.username ? currentUser.username : '<PERSON>ne Seite' %>
            </a>
        </h1>

        <!-- <PERSON><PERSON>-<PERSON><PERSON> (nur auf mobilen Geräten sichtbar) -->
        <button class="hamburger-menu-btn" aria-label="Menü öffnen" aria-expanded="false" aria-controls="user-nav-menu">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>

        <nav class="user-main-nav">
            <ul id="user-nav-menu" class="nav-menu">
                <!-- 1. Aktivitäten & Karte Dropdown -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Aktivitäten & Karte</a>
                    <ul class="dropdown-menu" style="display: none;">
                        <% if (currentUser && currentUser.username) { %>
                            <li><a href="/user/<%= encodeURIComponent(currentUser.username) %>/map">Meine Karte</a></li>
                            <li><a href="/user/activities">Meine Aktivitäten</a></li>
                            <li><a href="/user/activities/planned">Meine geplanten Aktivitäten</a></li>
                            <li><a href="/user/trip">Meine Reise</a></li>
                            <li><a href="/user/pois">Meine POIs</a></li>
                            <li><a href="/user/poi-import">POI Import</a></li>
                            <li><a href="/user/<%= encodeURIComponent(currentUser.username) %>/stats">Meine Statistiken</a></li>
                        <% } %>
                        <!-- Import Untermenü -->
                        <li class="dropdown-submenu">
                            <a href="#" class="dropdown-toggle">Import</a>
                            <ul class="dropdown-menu" style="display: none;">
                                <li><a href="/user/gpx-management">GPX-Dateien</a></li>
                                <li><a href="/user/url-gpx-import">GPX von URL</a></li>
                                <% if (currentUser && currentUser.google_drive_access_token) { %>
                                    <li><a href="/user/google-drive/files">Google Drive</a></li>
                                <% } %>
                                <% if (currentUser && currentUser.komoot_connected) { %>
                                    <li><a href="/user/komoot/tours">Komoot-Touren</a></li>
                                <% } %>
                                <% if (currentUser && currentUser.garmin_connected) { %>
                                    <li><a href="/user/garmin/activities">Garmin-Aktivitäten</a></li>
                                <% } %>
                            </ul>
                        </li>
                    </ul>
                </li>

                <!-- 2. Sozial Dropdown -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Sozial</a>
                    <ul class="dropdown-menu" style="display: none;">
                        <li><a href="/user/friends">Meine Freunde</a></li>
                        <li><a href="/user/notifications">Benachrichtigungen</a></li>
                    </ul>
                </li>

                <!-- 3. Einstellungen Dropdown -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Einstellungen</a>
                    <ul class="dropdown-menu" style="display: none;">
                        <li><a href="/user/settings">Profil & Konto</a></li>
                        <li><a href="/user/equipment">Meine Ausrüstung</a></li>
                        <li><a href="/user/sport-groups">Sport-Gruppen</a></li>
                        <li><a href="/user/map-styles">Karten-Stile</a></li>
                        <% if (currentUser) { %>
                            <li><a href="/user/komoot/settings">Komoot-Einstellungen</a></li>
                            <li><a href="/user/garmin/settings">Garmin-Einstellungen</a></li>
                        <% } %>
                    </ul>
                </li>

                <!-- Benachrichtigungen -->
                <li class="notifications-item">
                    <a href="/user/notifications" title="Benachrichtigungen" id="notificationsLink" class="notifications-link">
                        <span class="notifications-text">Benachrichtigungen</span>
                        <% if (typeof unreadNotificationsCount !== 'undefined' && unreadNotificationsCount > 0) { %>
                            <span class="notification-badge" id="notificationBadgeCount"><%= unreadNotificationsCount %></span>
                        <% } else { %>
                            <span class="notification-badge" id="notificationBadgeCount" style="display: none;">0</span>
                        <% } %>
                    </a>
                    <div id="notificationsDropdown" class="notifications-dropdown" style="display: none;">
                        <div id="dropdownContentHeader">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px; vertical-align: text-bottom;">
                                <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zM8 1.918l-.797.161A4.002 4.002 0 0 0 4 6c0 .628-.134 2.197-.459 3.742-.16.767-.376 1.566-.663 2.258h10.244c-.287-.692-.502-1.49-.663-2.258C12.134 8.197 12 6.628 12 6a4.002 4.002 0 0 0-3.203-3.92L8 1.917zM14.22 12c.223.447.481.801.78 1H1c.299-.199.557-.553.78-1C2.68 10.2 3 6.88 3 6c0-2.42 1.72-4.44 4.005-4.901a1 1 0 1 1 1.99 0A5.002 5.002 0 0 1 13 6c0 .88.32 4.2 1.22 6z"/>
                            </svg>
                            Benachrichtigungen
                        </div>
                        <div id="dropdownContentLoading">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="animation: spin 1s linear infinite; margin-right: 8px;">
                                <path d="M11.534 7h3.932a.25.25 0 0 1 .192.41l-1.966 2.36a.25.25 0 0 1-.384 0l-1.966-2.36a.25.25 0 0 1 .192-.41zm-11 2h3.932a.25.25 0 0 0 .192-.41L2.692 6.23a.25.25 0 0 0-.384 0L.342 8.59A.25.25 0 0 0 .534 9z"/>
                                <path fill-rule="evenodd" d="M8 3c-1.552 0-2.94.707-3.857 1.818a.5.5 0 1 1-.771-.636A6.002 6.002 0 0 1 13.917 7H12.9A5.002 5.002 0 0 0 8 3zM3.1 9a5.002 5.002 0 0 0 8.757 2.182.5.5 0 1 1 .771.636A6.002 6.002 0 0 1 2.083 9H3.1z"/>
                            </svg>
                            Lade...
                        </div>
                        <ul id="notificationsDropdownList">
                            <%# Inhalt wird per JS gefüllt %>
                        </ul>
                        <a href="/user/notifications" class="view-all-notifications">Alle Benachrichtigungen anzeigen</a>
                    </div>
                </li>

                <!-- Logout -->
                <li class="logout-item"><a href="/auth/logout">Logout</a></li>
            </ul>
        </nav>
    </div>
</header>

<!-- JavaScript für Dropdown-Menüs -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dropdown-Menüs
    const dropdowns = document.querySelectorAll('.dropdown');

    // Funktion zum Schließen aller Dropdowns
    function closeAllDropdowns() {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.style.display = 'none';
            menu.classList.remove('show');
        });
        document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
            toggle.classList.remove('active');
        });
    }

    // Schließe alle Dropdowns beim Laden der Seite
    closeAllDropdowns();

    // Event-Listener für Klicks außerhalb der Dropdowns
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.dropdown')) {
            closeAllDropdowns();
        }
    });

    // Event-Listener für jeden Dropdown
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');

        toggle.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();

            // Prüfe, ob dieses Dropdown bereits geöffnet ist
            const isCurrentlyOpen = menu.style.display === 'block' || menu.classList.contains('show');

            // Schließe alle Dropdowns, außer Untermenüs des aktuellen Dropdowns
            closeAllDropdowns();

            // Wenn das Dropdown nicht bereits geöffnet war, öffne es
            if (!isCurrentlyOpen) {
                menu.style.display = 'block';
                menu.classList.add('show');
                toggle.classList.add('active');
            }
        });
    });

    // Submenu-Handling
    const submenus = document.querySelectorAll('.dropdown-submenu');

    submenus.forEach(submenu => {
        const toggle = submenu.querySelector('.dropdown-toggle');
        const menu = submenu.querySelector('.dropdown-menu');

        toggle.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();

            // Prüfe, ob dieses Untermenü bereits geöffnet ist
            const isCurrentlyOpen = menu.style.display === 'block' || menu.classList.contains('show');

            // Schließe alle anderen Untermenüs auf der gleichen Ebene
            const parentDropdown = this.closest('.dropdown-submenu').parentElement;
            parentDropdown.querySelectorAll('.dropdown-submenu > .dropdown-menu').forEach(otherMenu => {
                if (otherMenu !== menu) {
                    otherMenu.style.display = 'none';
                    otherMenu.classList.remove('show');
                    otherMenu.previousElementSibling.classList.remove('active');
                }
            });

            // Toggle Untermenü
            if (!isCurrentlyOpen) {
                menu.style.display = 'block';
                menu.classList.add('show');
                toggle.classList.add('active');
            } else {
                menu.style.display = 'none';
                menu.classList.remove('show');
                toggle.classList.remove('active');
            }
        });
    });

    // Mobile Menu Toggle wird von hamburger_menu.js übernommen
});
</script>
