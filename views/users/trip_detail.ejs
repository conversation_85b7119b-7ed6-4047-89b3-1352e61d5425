<%# views/users/trip_detail.ejs %>

<h2 class="page-main-title"><%= locals.pageTitle %></h2>

    <main class="user-main-content">
        <div class="content-header">
            <h1><%= trip.title %></h1>
            <div class="content-actions">
                <% if (currentUser && currentUser.username) { %>
                    <a href="/user/trip" class="button button-secondary">Zurück zur Übersicht</a>
                <% } %>
                <% if (isOwner) { %>
                    <a href="/user/trip/<%= trip.id %>/edit" class="button button-primary">Bearbeiten</a>
                <% } %>
            </div>
        </div>

        <!-- Reise-Informationen -->
        <div class="trip-info-card">
            <div class="trip-header">
                <div class="trip-meta">
                    <% if (trip.start_date || trip.end_date) { %>
                        <div class="trip-dates">
                            <strong>Zeitraum:</strong>
                            <% if (trip.start_date && trip.end_date) { %>
                                <%= new Date(trip.start_date).toLocaleDateString('de-DE') %> - <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                            <% } else if (trip.start_date) { %>
                                ab <%= new Date(trip.start_date).toLocaleDateString('de-DE') %>
                            <% } else { %>
                                bis <%= new Date(trip.end_date).toLocaleDateString('de-DE') %>
                            <% } %>
                        </div>
                    <% } %>

                    <div class="trip-status">
                        <% if (trip.is_public) { %>
                            <a href="/show/trip/<%= trip.share_uuid %>" target="_blank" class="status-badge status-public" style="text-decoration: none; cursor: pointer;" title="Öffentliche Ansicht öffnen">🌐 Öffentlich</a>
                        <% } else { %>
                            <span class="status-badge status-private">🔒 Privat</span>
                        <% } %>
                    </div>
                </div>

                <% if (trip.description) { %>
                    <div class="trip-description">
                        <p><%= trip.description %></p>
                    </div>
                <% } %>
            </div>

            <!-- Statistiken -->
            <div class="trip-stats">
                <div class="stat-item">
                    <div class="stat-number"><%= stats.activities_count %></div>
                    <div class="stat-label">Aktivitäten</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.planned_routes_count %></div>
                    <div class="stat-label">Geplante Routen</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><%= stats.pois_count %></div>
                    <div class="stat-label">POIs</div>
                </div>
                <% if (stats.total_distance > 0) { %>
                    <div class="stat-item">
                        <div class="stat-number"><%= (stats.total_distance / 1000).toFixed(1).replace('.', ',') %> km</div>
                        <div class="stat-label">Gesamtdistanz</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><%= Math.round(stats.total_elevation_gain) %> m</div>
                        <div class="stat-label">Höhenmeter</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <%= Math.floor(stats.total_moving_time / 3600) %>h <%= Math.floor((stats.total_moving_time % 3600) / 60) %>m
                        </div>
                        <div class="stat-label">Bewegungszeit</div>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Karte und Bilder -->
        <% if ((geoData && (geoData.activities.length > 0 || geoData.plannedRoutes.length > 0 || geoData.pois.length > 0)) || (images && images.length > 0)) { %>
            <div class="trip-visual-section">
                <div class="visual-grid">
                    <!-- Karte -->
                    <% if (geoData && (geoData.activities.length > 0 || geoData.plannedRoutes.length > 0 || geoData.pois.length > 0)) { %>
                        <div class="map-container">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <h3 style="margin: 0;">Karte der Reise</h3>
                                <button id="maximizeMapBtn" class="button button-secondary" style="padding: 8px 12px; font-size: 0.9em;">
                                    ⛶ Maximieren
                                </button>
                            </div>
                            <div id="tripMap" style="height: 500px; width: 100%; border-radius: 8px; overflow: hidden;"></div>
                            <div class="map-legend" style="margin-top: 10px; font-size: 0.9em; color: #666;">
                                <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                                    <% if (geoData.activities.length > 0) { %>
                                        <span>🏃‍♂️ Aktivitäten (<%= geoData.activities.length %>)</span>
                                    <% } %>
                                    <% if (geoData.plannedRoutes.length > 0) { %>
                                        <span>🗺️ Geplante Routen (<%= geoData.plannedRoutes.length %>)</span>
                                    <% } %>
                                    <% if (geoData.pois.length > 0) { %>
                                        <span>📍 POIs (<%= geoData.pois.length %>)</span>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% } %>

                    <!-- Bilder -->
                    <% if (images && images.length > 0) { %>
                        <div class="images-container">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <h3 style="margin: 0;">Bilder der Reise (<%= totalImages || images.length %>)</h3>
                                <% if (totalImages > 12) { %>
                                    <button id="loadMoreImagesBtn" class="button button-secondary" style="padding: 8px 12px; font-size: 0.9em;">
                                        Mehr laden (<%= totalImages - 12 %> weitere)
                                    </button>
                                <% } %>
                            </div>
                            <div class="images-grid" id="imagesGrid">
                                <% images.forEach((image, index) => { %>
                                    <div class="image-item" onclick="openImageModal(<%- index %>)">
                                        <img src="/uploads/activity_photos/<%= image.activity_id %>/<%= image.base_filename %>_m.jpeg" alt="<%= image.caption || image.activity_name %>" loading="lazy">
                                        <div class="image-overlay">
                                            <div class="image-info">
                                                <div class="image-activity"><%= image.activity_name %></div>
                                                <% if (image.caption) { %>
                                                    <div class="image-caption"><%= image.caption %></div>
                                                <% } %>
                                                <div class="image-date"><%= new Date(image.start_date_local).toLocaleDateString('de-DE') %></div>
                                            </div>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                            <div id="loadingMoreImages" style="display: none; text-align: center; padding: 20px;">
                                <p>Lade weitere Bilder...</p>
                            </div>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } %>

        <!-- Aktivitäten -->
        <% if (activities && activities.length > 0) { %>
            <div class="trip-section">
                <h2>Erledigte Aktivitäten (<%= activities.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Sport</th>
                                <th>Datum</th>
                                <th>Distanz</th>
                                <th>Höhenmeter</th>
                                <th>Zeit</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% activities.forEach(activity => { %>
                                <tr>
                                    <td>
                                        <% if (isOwner) { %>
                                            <a href="/user/activity/<%= activity.activity_id %>"><%= activity.activity_name %></a>
                                        <% } else { %>
                                            <% if (activity.share_uuid) { %>
                                                <a href="/show/activity/<%= activity.share_uuid %>"><%= activity.activity_name %></a>
                                            <% } else { %>
                                                <%= activity.activity_name %>
                                            <% } %>
                                        <% } %>
                                    </td>
                                    <td><%= activity.sport_type %></td>
                                    <td><%= new Date(activity.start_date_local).toLocaleDateString('de-DE') %></td>
                                    <td class="number"><%= (activity.distance / 1000).toFixed(1).replace('.', ',') %> km</td>
                                    <td class="number"><%= Math.round(activity.total_elevation_gain) %> m</td>
                                    <td class="number">
                                        <%= Math.floor(activity.moving_time / 3600) %>h <%= Math.floor((activity.moving_time % 3600) / 60) %>m
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-activity" style="display: inline;">
                                                <input type="hidden" name="activityId" value="<%= activity.activity_id %>">
                                                <button type="submit" class="button-link-small button-danger"
                                                        onclick="return confirm('Aktivität von der Reise entfernen?')"
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
            <%# Mobile Card Layout (wird nur auf mobilen Geräten angezeigt) %>
            <div class="mobile-cards-container">
                <% activities.forEach(activity => { %>
                    <div class="mobile-card <%= !activity.is_owned_by_user ? 'shared' : 'owned' %>">
                        <div class="mobile-card-header">
                            <% if (isOwner) { %>
                                <a href="/user/activity/<%= activity.activity_id %>" class="mobile-card-title" title="Details für '<%= activity.activity_name || '(Unbenannt)' %>'">
                                    <%= activity.activity_name || '(Unbenannt)' %>
                                </a>                                
                            <% } else { %>
                                <% if (activity.share_uuid) { %>
                                    <a href="/show/activity/<%= activity.share_uuid %>" class="mobile-card-title" title="Details für '<%= activity.activity_name || '(Unbenannt)' %>'">
                                        <%= activity.activity_name || '(Unbenannt)' %>
                                    </a>                                      
                                <% } else { %>
                                    <%= activity.activity_name %>
                                <% } %>
                            <% } %>                            

                            <div class="mobile-card-date">
                                <%= activity.start_date_local ? new Date(activity.start_date_local).toLocaleDateString('de-DE', {day:'2-digit', month:'2-digit', year:'numeric'}) : '?' %>
                            </div>
                        </div>

                        <div class="mobile-card-meta">
                            <div class="mobile-card-meta-item">
                                <div class="mobile-card-meta-label">Sportart</div>
                                <div class="mobile-card-meta-value"><%= activity.sport_type %></div>
                            </div>
                            <div class="mobile-card-meta-item">
                                <div class="mobile-card-meta-label">Distanz</div>
                                <div class="mobile-card-meta-value"><%= activity.distance != null ? (activity.distance / 1000).toFixed(1).replace('.',',') + ' km' : '-' %></div>
                            </div>
                            <div class="mobile-card-meta-item">
                                <div class="mobile-card-meta-label">Höhenmeter</div>
                                <div class="mobile-card-meta-value"><%= activity.total_elevation_gain != null ? Math.round(activity.total_elevation_gain) + ' hm' : '-' %></div>
                            </div>
                            <div class="mobile-card-meta-item">
                                <div class="mobile-card-meta-label">Dauer</div>
                                <div class="mobile-card-meta-value"><%= Math.floor(activity.moving_time / 3600) %>h <%= Math.floor((activity.moving_time % 3600) / 60) %>m</div>
                            </div>
                        </div>

                        <div class="mobile-card-actions">
                            <a href="/show/activity/<%= activity.share_uuid %>" class="button-primary" title="Details ansehen">Details</a>
                            <% if (isOwner) { %>
                                <form action="/user/trip/<%= trip.id %>/remove-activity" method="POST" style="display: inline;">
                                    <input type="hidden" name="activityId" value="<%= activity.activity_id %>">
                                    <button type="submit" class="button-delete"
                                        onsubmit="return confirm('Aktivität von der Reise wirklich entfernen?');"
                                        title="Von Reise entfernen">Entfernen</button>
                                </form>
                            <% } %>
                        </div>
                    </div>
                <% }); %>
            </div>            
        <% } %>

        <!-- Geplante Routen -->
        <% if (plannedRoutes && plannedRoutes.length > 0) { %>
            <div class="trip-section">
                <h2>Geplante Routen (<%= plannedRoutes.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Sport</th>
                                <th>Distanz</th>
                                <th>Höhenmeter</th>
                                <th>Dauer</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% plannedRoutes.forEach(route => { %>
                                <tr>
                                    <td>
                                        <a href="/user/activity/planned/<%= route.planned_route_id %>"><%= route.name %></a>
                                    </td>
                                    <td><%= route.sport_type %></td>
                                    <td class="number"><%= (route.distance_m / 1000).toFixed(1).replace('.', ',') %> km</td>
                                    <td class="number"><%= Math.round(route.elevation_gain_m) %> m</td>
                                    <td class="number">
                                        <% if (route.duration) { %>
                                            <%= Math.floor(route.duration / 3600) %>h <%= Math.floor((route.duration % 3600) / 60) %>m
                                        <% } else { %>
                                            -
                                        <% } %>
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-planned-route" style="display: inline;">
                                                <input type="hidden" name="plannedRouteId" value="<%= route.planned_route_id %>">
                                                <button type="submit" class="button-link-small button-danger"
                                                        onclick="return confirm('Geplante Route von der Reise entfernen?')"
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        <% } %>

        <!-- POIs -->
        <% if (pois && pois.length > 0) { %>
            <div class="trip-section">
                <h2>Points of Interest (<%= pois.length %>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Titel</th>
                                <th>Typ</th>
                                <th>Beschreibung</th>
                                <% if (isOwner) { %>
                                    <th>Aktionen</th>
                                <% } %>
                            </tr>
                        </thead>
                        <tbody>
                            <% pois.forEach(poi => { %>
                                <tr>
                                    <td>
                                        <a href="/user/poi/<%= poi.poi_id %>"><%= poi.title %></a>
                                    </td>
                                    <td><%= poi.poi_type %></td>
                                    <td>
                                        <% if (poi.description) { %>
                                            <%= poi.description.substring(0, 100) %><%= poi.description.length > 100 ? '...' : '' %>
                                        <% } else { %>
                                            <span class="text-muted">Keine Beschreibung</span>
                                        <% } %>
                                    </td>
                                    <% if (isOwner) { %>
                                        <td class="actions">
                                            <form method="POST" action="/user/trip/<%= trip.id %>/remove-poi" style="display: inline;">
                                                <input type="hidden" name="poiId" value="<%= poi.poi_id %>">
                                                <button type="submit" class="button-link-small button-danger"
                                                        onclick="return confirm('POI von der Reise entfernen?')"
                                                        title="Von Reise entfernen">Entfernen</button>
                                            </form>
                                        </td>
                                    <% } %>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
            <%# Mobile Card Layout (wird nur auf mobilen Geräten angezeigt) %>
            <div class="mobile-cards-container">
                <% pois.forEach(poi => { %>
                    <div class="mobile-card owned">
                        <div class="mobile-card-header">
                            <a href="/user/poi/<%= poi.poi_id %>" class="mobile-card-title"><%= poi.title %></a>                                
                        </div>

                        <div class="mobile-card-meta">
                            <div class="mobile-card-meta-item">
                                <div class="mobile-card-meta-label">Beschreibung</div>
                                <div class="mobile-card-meta-value"><%= poi.description %></div>
                            </div>
                        </div>

                        <div class="mobile-card-actions">
                            <a href="/user/poi/<%= poi.poi_id %>" class="button-primary" title="Details ansehen">Details</a>
                            <% if (isOwner) { %>
                                <form action="/user/trip/<%= trip.id %>/remove-poi" method="POST" style="display: inline;">
                                    <input type="hidden" name="poiId" value="<%= poi.poi_id %>">
                                    <button type="submit" class="button-delete"
                                        onsubmit="return confirm('POI von der Reise entfernen?');"
                                        title="Von Reise entfernen">Entfernen</button>
                                </form>
                            <% } %>
                        </div>
                    </div>
                <% }); %>
            </div>              
        <% } %>

        <!-- Leere Zustände -->
        <% if ((!activities || activities.length === 0) && (!plannedRoutes || plannedRoutes.length === 0) && (!pois || pois.length === 0)) { %>
            <div class="empty-state">
                <h3>Noch keine Inhalte verknüpft</h3>
                <p>Fügen Sie Aktivitäten, geplante Routen oder POIs zu Ihrer Reise hinzu.</p>
                <% if (isOwner) { %>
                    <div class="empty-state-actions">
                        <a href="/user/activities" class="button button-primary">Aktivitäten verknüpfen</a>
                        <a href="/user/activities/planned" class="button button-secondary">Geplante Routen verknüpfen</a>
                        <a href="/user/pois" class="button button-secondary">POIs verknüpfen</a>
                    </div>
                    <div style="margin-top: 1.5rem; padding: 1rem; background: #e8f4fd; border-radius: 8px; font-size: 0.9em;">
                        <h4 style="margin: 0 0 0.5rem 0; color: #0c5460;">💡 Tipp: Inhalte hinzufügen</h4>
                        <p style="margin: 0;">
                            <strong>Aktivitäten:</strong> Gehen Sie zu <a href="/user/activities">Meine Aktivitäten</a> und verwenden Sie die Checkboxen für Bulk-Aktionen.<br>
                            <strong>Geplante Routen:</strong> Erstellen Sie neue Routen unter <a href="/user/activities/planned">Geplante Routen</a> oder verknüpfen Sie bestehende.<br>
                            <strong>POIs:</strong> Besuchen Sie <a href="/user/pois">Meine POIs</a> oder importieren Sie neue von URLs.
                        </p>
                        <div style="margin-top: 1rem; display: flex; gap: 10px; flex-wrap: wrap;">
                            <button id="suggestActivitiesBtn" class="button button-primary" style="font-size: 0.9em;">🎯 Aktivitäten vorschlagen</button>
                            <button id="bulkAddActivitiesBtn" class="button button-secondary" style="font-size: 0.9em;">📋 Aktivitäten hinzufügen</button>
                            <button id="bulkAddPlannedRoutesBtn" class="button button-secondary" style="font-size: 0.9em;">🗺️ Routen hinzufügen</button>
                            <button id="bulkAddPoisBtn" class="button button-secondary" style="font-size: 0.9em;">📍 POIs hinzufügen</button>
                        </div>
                    </div>
                <% } %>
            </div>
        <% } %>
    </main>

    <style>
        .trip-info-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .trip-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .trip-dates {
            color: #6c757d;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: bold;
        }

        .status-public {
            background: #d4edda;
            color: #155724;
        }

        .status-private {
            background: #f8d7da;
            color: #721c24;
        }

        .trip-description {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .trip-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .trip-section {
            margin-bottom: 2rem;
        }

        .trip-section h2 {
            margin-bottom: 1rem;
            color: #495057;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .empty-state-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .text-muted {
            color: #6c757d;
            font-style: italic;
        }

        .trip-visual-section {
            margin-bottom: 2rem;
        }

        .visual-grid {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .map-container, .images-container {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .map-container {
            width: 100%;
        }

        .images-container {
            width: 100%;
        }

        .map-container h3, .images-container h3 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: #495057;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }

        .image-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .image-item:hover {
            transform: scale(1.05);
        }

        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 1rem;
            transform: translateY(100%);
            transition: transform 0.2s ease;
        }

        .image-item:hover .image-overlay {
            transform: translateY(0);
        }

        .image-activity {
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .image-caption {
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .image-date {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Image Modal */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .image-modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            height: 90%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .image-modal img {
            max-width: 100%;
            max-height: 80%;
            object-fit: contain;
        }

        .image-modal-info {
            color: white;
            text-align: center;
            margin-top: 1rem;
        }

        .image-modal-close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .image-modal-close:hover {
            color: #bbb;
        }

        /* Navigation Arrows */
        .image-modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 24px;
            font-weight: bold;
            z-index: 1001;
            transition: all 0.3s ease;
            user-select: none;
        }

        .image-modal-nav:hover {
            background: rgba(0,0,0,0.8);
            transform: translateY(-50%) scale(1.1);
        }

        .image-modal-prev {
            left: 20px;
        }

        .image-modal-next {
            right: 20px;
        }

        .image-modal-nav.disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }

        .image-modal-nav.disabled:hover {
            background: rgba(0,0,0,0.5);
            transform: translateY(-50%) scale(1);
        }

        /* Clickable Areas */
        .image-modal-click-area {
            position: absolute;
            top: 80px; /* Platz für Close-Button lassen */
            bottom: 0;
            width: 30%;
            z-index: 1000;
            cursor: pointer;
        }

        .image-modal-click-left {
            left: 0;
        }

        .image-modal-click-right {
            right: 0;
        }

        /* Loading Spinner */
        .image-modal-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1002;
            display: none;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Image Counter */
        .image-modal-counter {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
        }

        @media (max-width: 768px) {
            .trip-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .trip-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .map-container, .images-container {
                background: white;
                border-radius: 4px;
                padding: 2px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }            

            .empty-state-actions {
                flex-direction: column;
                align-items: center;
            }

            .images-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }

            .image-modal-content{
                padding: 5px;
            }

            .image-modal img{
                max-height: 90%;
            }

            /* Mobile Navigation */
            .image-modal-nav {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }

            .image-modal-prev {
                left: 10px;
            }

            .image-modal-next {
                right: 10px;
            }

            .image-modal-counter {
                top: 10px;
                font-size: 12px;
                padding: 6px 12px;
            }

            .image-modal-click-area {
                width: 35%;
            }

        }
    </style>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <span class="image-modal-close">&times;</span>

            <!-- Navigation Arrows -->
            <div class="image-modal-nav image-modal-prev" id="modalPrevBtn">
                <span>&#8249;</span>
            </div>
            <div class="image-modal-nav image-modal-next" id="modalNextBtn">
                <span>&#8250;</span>
            </div>

            <!-- Clickable Areas for Navigation -->
            <div class="image-modal-click-area image-modal-click-left" id="modalClickLeft"></div>
            <div class="image-modal-click-area image-modal-click-right" id="modalClickRight"></div>

            <!-- Loading Spinner -->
            <div class="image-modal-loading" id="modalLoading">
                <div class="loading-spinner"></div>
            </div>

            <img id="modalImage" src="" alt="" style="display: none;">

            <!-- Image Counter -->
            <div class="image-modal-counter" id="modalImageCounter">
                <span id="modalCurrentIndex">1</span> / <span id="modalTotalImages">1</span>
            </div>

            <div class="image-modal-info">
                <div id="modalImageActivity"></div>
                <div id="modalImageCaption"></div>
                <div id="modalImageDate"></div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <script>
        // Globale Variablen
        let tripMap;
        let allImages = <%- JSON.stringify(images || []) %>;
        const geoData = <%- JSON.stringify(geoData || {activities: [], plannedRoutes: [], pois: []}) %>;
        const totalImages = <%= totalImages || 0 %>;
        const tripId = <%= trip.id %>;
        const isOwner = <%= isOwner ? 'true' : 'false' %>;
        const shareUuid = '<%= trip.share_uuid %>';
        let currentImagePage = 1;
        let currentImageIndex = 0; // Für Navigation zwischen Bildern

        // Karte initialisieren
        <% if (geoData && (geoData.activities.length > 0 || geoData.plannedRoutes.length > 0 || geoData.pois.length > 0)) { %>
        document.addEventListener('DOMContentLoaded', function() {
            initTripMap();
        });

        function initTripMap() {
            // Karte erstellen
            tripMap = L.map('tripMap').setView([51.1657, 10.4515], 6); // Deutschland Zentrum

            // Tile Layer hinzufügen
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(tripMap);

            const allBounds = [];

            // Aktivitäten laden
            geoData.activities.forEach(activity => {
                if (activity.gpx) {
                    fetch(`/gpx_files/${activity.id}.gpx`)
                        .then(response => response.text())
                        .then(gpxText => {
                            const parser = new DOMParser();
                            const gpxDoc = parser.parseFromString(gpxText, 'text/xml');
                            const trackPoints = gpxDoc.querySelectorAll('trkpt');

                            if (trackPoints.length > 0) {
                                const coordinates = Array.from(trackPoints).map(point => [
                                    parseFloat(point.getAttribute('lat')),
                                    parseFloat(point.getAttribute('lon'))
                                ]);

                                const polyline = L.polyline(coordinates, {
                                    color: '#007bff',
                                    weight: 3,
                                    opacity: 0.8
                                }).addTo(tripMap);

                                polyline.bindPopup(`
                                    <strong>${activity.activity_name}</strong><br>
                                    Sport: ${activity.sport_type}<br>
                                    Distanz: ${(activity.distance / 1000).toFixed(1)} km<br>
                                    Datum: ${new Date(activity.start_date_local).toLocaleDateString('de-DE')}
                                `);

                                allBounds.push(...coordinates);
                            }

                            // Prüfe ob alle Daten geladen sind
                            if (window.checkAndFitBounds) {
                                window.checkAndFitBounds();
                            }
                        })
                        .catch(error => {
                            console.error('Error loading GPX:', error);
                            if (window.checkAndFitBounds) {
                                window.checkAndFitBounds();
                            }
                        });
                }
            });

            // Geplante Routen laden
            geoData.plannedRoutes.forEach(route => {
                if (route.gpx_filename) {
                    fetch(`/gpx_files/planned/${route.gpx_filename}`)
                        .then(response => response.text())
                        .then(gpxText => {
                            const parser = new DOMParser();
                            const gpxDoc = parser.parseFromString(gpxText, 'text/xml');
                            const trackPoints = gpxDoc.querySelectorAll('trkpt, rtept');

                            if (trackPoints.length > 0) {
                                const coordinates = Array.from(trackPoints).map(point => [
                                    parseFloat(point.getAttribute('lat')),
                                    parseFloat(point.getAttribute('lon'))
                                ]);

                                const polyline = L.polyline(coordinates, {
                                    color: '#28a745',
                                    weight: 3,
                                    opacity: 0.8,
                                    dashArray: '5, 5'
                                }).addTo(tripMap);

                                polyline.bindPopup(`
                                    <strong>${route.name}</strong><br>
                                    Sport: ${route.sport_type}<br>
                                    Distanz: ${(route.distance_m / 1000).toFixed(1)} km<br>
                                    <em>Geplante Route</em>
                                `);

                                allBounds.push(...coordinates);
                            }

                            // Prüfe ob alle Daten geladen sind
                            if (window.checkAndFitBounds) {
                                window.checkAndFitBounds();
                            }
                        })
                        .catch(error => {
                            console.error('Error loading GPX:', error);
                            if (window.checkAndFitBounds) {
                                window.checkAndFitBounds();
                            }
                        });
                }
            });

            // POIs hinzufügen
            geoData.pois.forEach(poi => {
                const marker = L.marker([poi.latitude, poi.longitude]).addTo(tripMap);
                marker.bindPopup(`
                    <strong>${poi.title}</strong><br>
                    Typ: ${poi.poi_type}<br>
                    ${poi.description ? poi.description.substring(0, 100) + '...' : ''}
                `);
                allBounds.push([poi.latitude, poi.longitude]);
            });

            // Karte an alle Punkte anpassen
            if (allBounds.length > 0) {
                // Warte bis alle GPX-Daten geladen sind
                let loadedCount = 0;
                const totalToLoad = geoData.activities.length + geoData.plannedRoutes.length;

                function checkAndFitBounds() {
                    loadedCount++;
                    if (loadedCount >= totalToLoad || totalToLoad === 0) {
                        setTimeout(() => {
                            const bounds = L.latLngBounds(allBounds);
                            window.mapBounds = bounds; // Für Maximierung speichern
                            tripMap.fitBounds(bounds, {
                                padding: [30, 30],
                                maxZoom: 16
                            });
                        }, 500);
                    }
                }

                // Wenn keine GPX-Daten zu laden sind, sofort fitten
                if (totalToLoad === 0) {
                    checkAndFitBounds();
                }

                window.checkAndFitBounds = checkAndFitBounds;
            }

            // Maximieren-Button
            document.getElementById('maximizeMapBtn').addEventListener('click', function() {
                toggleMaximizeMap();
            });
        }
        <% } %>

        // Browser-Maximierung Funktionen
        let isMapMaximized = false;
        let originalMapHeight = '500px';

        function toggleMaximizeMap() {
            const mapDiv = document.getElementById('tripMap');
            const maximizeBtn = document.getElementById('maximizeMapBtn');
            const mapContainer = document.querySelector('.map-container');

            if (!isMapMaximized) {
                // Maximieren
                originalMapHeight = mapDiv.style.height;
                originalMapWidth = mapDiv.style.width;

                // Berechne verfügbare Dimensionen
                const viewportHeight = window.innerHeight;
                const mapContainerRect = mapContainer.getBoundingClientRect();
                const availableHeight = viewportHeight - mapContainerRect.top - 50; // 50px Puffer

                // Setze Container-Breite und Karten-Dimensionen
                mapContainer.style.maxWidth = 'none';
                mapDiv.style.height = Math.max(availableHeight, 600) + 'px';
                mapDiv.style.transition = 'height 0.3s ease';
                maximizeBtn.innerHTML = '⛶ Minimieren';
                isMapMaximized = true;

                // Scroll zur Karte
                mapContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });

                setTimeout(() => {
                    tripMap.invalidateSize();
                    if (window.mapBounds) {
                        tripMap.fitBounds(window.mapBounds, {
                            padding: [30, 30],
                            maxZoom: 16
                        });
                    }
                }, 350);
            } else {
                // Minimieren - zurück zu ursprünglichen Werten
                mapContainer.style.width = '';
                mapContainer.style.maxWidth = '';
                mapDiv.style.height = originalMapHeight;
                mapDiv.style.transition = 'all 0.3s ease';
                maximizeBtn.innerHTML = '⛶ Maximieren';
                isMapMaximized = false;

                setTimeout(() => {
                    tripMap.invalidateSize();
                    if (window.mapBounds) {
                        tripMap.fitBounds(window.mapBounds, {
                            padding: [30, 30],
                            maxZoom: 16
                        });
                    }
                }, 350);
            }
        }

        // Window Resize Event für maximierte Karte
        window.addEventListener('resize', function() {
            if (isMapMaximized) {
                const mapDiv = document.getElementById('tripMap');
                const mapContainer = document.querySelector('.map-container');
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;
                const mapContainerRect = mapContainer.getBoundingClientRect();
                const availableHeight = viewportHeight - mapContainerRect.top - 50;

                mapContainer.style.width = availableWidth + 'px';
                mapDiv.style.height = Math.max(availableHeight, 600) + 'px';

                setTimeout(() => {
                    tripMap.invalidateSize();
                    if (window.mapBounds) {
                        tripMap.fitBounds(window.mapBounds, {
                            padding: [30, 30],
                            maxZoom: 16
                        });
                    }
                }, 100);
            }
        });

        // Bilder-Paginierung und Bulk-Aktionen
        document.addEventListener('DOMContentLoaded', function() {
            const loadMoreBtn = document.getElementById('loadMoreImagesBtn');
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', loadMoreImages);
            }

            // Bulk-Aktionen Buttons
            const suggestActivitiesBtn = document.getElementById('suggestActivitiesBtn');
            const bulkAddActivitiesBtn = document.getElementById('bulkAddActivitiesBtn');
            const bulkAddPlannedRoutesBtn = document.getElementById('bulkAddPlannedRoutesBtn');
            const bulkAddPoisBtn = document.getElementById('bulkAddPoisBtn');

            if (suggestActivitiesBtn) {
                suggestActivitiesBtn.addEventListener('click', function() {
                    window.location.href = `/user/trip/${tripId}/suggest-activities`;
                });
            }

            if (bulkAddActivitiesBtn) {
                bulkAddActivitiesBtn.addEventListener('click', function() {
                    window.location.href = '/user/activities';
                });
            }

            if (bulkAddPlannedRoutesBtn) {
                bulkAddPlannedRoutesBtn.addEventListener('click', function() {
                    window.location.href = '/user/activities/planned';
                });
            }

            if (bulkAddPoisBtn) {
                bulkAddPoisBtn.addEventListener('click', function() {
                    window.location.href = '/user/pois';
                });
            }
        });

        function loadMoreImages() {
            const loadMoreBtn = document.getElementById('loadMoreImagesBtn');
            const loadingDiv = document.getElementById('loadingMoreImages');

            loadMoreBtn.style.display = 'none';
            loadingDiv.style.display = 'block';

            currentImagePage++;

            // Verwende unterschiedliche API-URLs je nach Ansicht
            const apiUrl = isOwner
                ? `/user/api/trip/${tripId}/images?page=${currentImagePage}&limit=12`
                : `/show/api/public/trip/${shareUuid}/images?page=${currentImagePage}&limit=12`;

            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.images.length > 0) {
                        // Bilder zu allImages hinzufügen
                        allImages = allImages.concat(data.images);

                        // Neue Bilder zum Grid hinzufügen
                        const imagesGrid = document.getElementById('imagesGrid');
                        data.images.forEach((image, index) => {
                            const imageIndex = allImages.length - data.images.length + index;
                            const imageItem = document.createElement('div');
                            imageItem.className = 'image-item';
                            imageItem.onclick = () => openImageModal(imageIndex);
                            imageItem.innerHTML = `
                                <img src="/uploads/activity_photos/${image.activity_id}/${image.base_filename}_m.jpeg" alt="${image.caption || image.activity_name}" loading="lazy">
                                <div class="image-overlay">
                                    <div class="image-info">
                                        <div class="image-activity">${image.activity_name}</div>
                                        ${image.caption ? `<div class="image-caption">${image.caption}</div>` : ''}
                                        <div class="image-date">${new Date(image.start_date_local).toLocaleDateString('de-DE')}</div>
                                    </div>
                                </div>
                            `;
                            imagesGrid.appendChild(imageItem);
                        });

                        // Button-Text aktualisieren
                        const remainingImages = totalImages - allImages.length;
                        if (remainingImages > 0 && data.hasMore) {
                            loadMoreBtn.innerHTML = `Mehr laden (${remainingImages} weitere)`;
                            loadMoreBtn.style.display = 'block';
                        }
                    }

                    loadingDiv.style.display = 'none';
                })
                .catch(error => {
                    console.error('Fehler beim Laden der Bilder:', error);
                    loadingDiv.innerHTML = '<p style="color: red;">Fehler beim Laden der Bilder.</p>';
                    setTimeout(() => {
                        loadingDiv.style.display = 'none';
                        loadMoreBtn.style.display = 'block';
                    }, 3000);
                });
        }

        // Bild-Modal Funktionen
        function openImageModal(index) {
            currentImageIndex = index;
            showImageAtIndex(index);

            const modal = document.getElementById('imageModal');
            modal.style.display = 'block';

            // Touch-Events für Swipe-Gesten hinzufügen
            addTouchEvents();
        }

        function showImageAtIndex(index) {
            if (index < 0 || index >= allImages.length) return;

            const modalImage = document.getElementById('modalImage');
            const modalActivity = document.getElementById('modalImageActivity');
            const modalCaption = document.getElementById('modalImageCaption');
            const modalDate = document.getElementById('modalImageDate');
            const modalCurrentIndex = document.getElementById('modalCurrentIndex');
            const modalTotalImages = document.getElementById('modalTotalImages');
            const prevBtn = document.getElementById('modalPrevBtn');
            const nextBtn = document.getElementById('modalNextBtn');
            const modalLoading = document.getElementById('modalLoading');

            const image = allImages[index];

            // Ladekreis anzeigen und Bild verstecken
            modalLoading.style.display = 'block';
            modalImage.style.display = 'none';

            // Neue Bild-URL setzen
            const newImageSrc = `/uploads/activity_photos/${image.activity_id}/${image.base_filename}_o.jpeg`;

            // Bild vorladen
            const tempImage = new Image();
            tempImage.onload = function() {
                // Bild ist geladen - anzeigen und Ladekreis verstecken
                modalImage.src = newImageSrc;
                modalImage.alt = image.caption || image.activity_name;
                modalImage.style.display = 'block';
                modalLoading.style.display = 'none';
            };

            tempImage.onerror = function() {
                // Fehler beim Laden - Ladekreis verstecken und Platzhalter anzeigen
                modalImage.src = newImageSrc; // Trotzdem versuchen anzuzeigen
                modalImage.alt = image.caption || image.activity_name;
                modalImage.style.display = 'block';
                modalLoading.style.display = 'none';
            };

            // Lade-Prozess starten
            tempImage.src = newImageSrc;

            // Metadaten aktualisieren
            modalActivity.textContent = image.activity_name;
            modalCaption.textContent = image.caption || '';
            modalDate.textContent = new Date(image.start_date_local).toLocaleDateString('de-DE');

            // Bildindex aktualisieren
            modalCurrentIndex.textContent = index + 1;
            modalTotalImages.textContent = allImages.length;

            // Navigation-Buttons aktivieren/deaktivieren
            prevBtn.classList.toggle('disabled', index === 0);
            nextBtn.classList.toggle('disabled', index === allImages.length - 1);
        }

        function navigateImage(direction) {
            const newIndex = currentImageIndex + direction;
            if (newIndex >= 0 && newIndex < allImages.length) {
                currentImageIndex = newIndex;
                showImageAtIndex(newIndex);
            }
        }

        // Touch-Events für Swipe-Gesten
        let touchStartX = 0;
        let touchEndX = 0;

        function addTouchEvents() {
            const modal = document.getElementById('imageModal');

            modal.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            }, { passive: true });

            modal.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            }, { passive: true });
        }

        function handleSwipe() {
            const swipeThreshold = 50; // Mindestdistanz für Swipe
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe nach links - nächstes Bild
                    navigateImage(1);
                } else {
                    // Swipe nach rechts - vorheriges Bild
                    navigateImage(-1);
                }
            }
        }

        // Modal schließen und Event-Handler
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('imageModal');
            const closeBtn = document.querySelector('.image-modal-close');
            const prevBtn = document.getElementById('modalPrevBtn');
            const nextBtn = document.getElementById('modalNextBtn');
            const clickLeft = document.getElementById('modalClickLeft');
            const clickRight = document.getElementById('modalClickRight');

            // Schließen-Button
            if (closeBtn) {
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    modal.style.display = 'none';
                });
            }

            // Navigation-Buttons
            if (prevBtn) {
                prevBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (!prevBtn.classList.contains('disabled')) {
                        navigateImage(-1);
                    }
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (!nextBtn.classList.contains('disabled')) {
                        navigateImage(1);
                    }
                });
            }

            // Klickbare Bereiche
            if (clickLeft) {
                clickLeft.addEventListener('click', function(e) {
                    e.stopPropagation();
                    navigateImage(-1);
                });
            }

            if (clickRight) {
                clickRight.addEventListener('click', function(e) {
                    e.stopPropagation();
                    navigateImage(1);
                });
            }

            // Modal schließen bei Klick außerhalb
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });

            // Tastatur-Navigation
            document.addEventListener('keydown', function(event) {
                if (modal.style.display === 'block') {
                    switch(event.key) {
                        case 'Escape':
                            modal.style.display = 'none';
                            break;
                        case 'ArrowLeft':
                            event.preventDefault();
                            navigateImage(-1);
                            break;
                        case 'ArrowRight':
                            event.preventDefault();
                            navigateImage(1);
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>
