<%# views/welcome.ejs %>
    <style>
        .welcome-header {
            text-align: center;
            padding: 2rem 1rem; /* Padding für kleinere Bildschirme angepasst */
            background-color: #007bff;
            color: white;
            margin-bottom: 2rem;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .welcome-header h1 {
            font-size: 2.2rem; /* Etwas kleiner für bessere mobile Darstellung */
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .welcome-header p {
            font-size: 1.1rem; /* Etwas kleiner */
            opacity: 0.9;
            max-width: 90%; /* Prozentuale Breite, um Überlaufen zu verhindern */
            margin: 0 auto;
        }

        .welcome-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem; /* Padding links/rechts für den Container */
        }

        .welcome-content {
            display: flex;
            flex-wrap: wrap; /* <PERSON><PERSON><PERSON>bt Umbruch auf kleineren Bildschirmen */
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .welcome-main {
            flex: 1; /* Nimmt verfügbaren Platz */
            min-width: 280px; /* Mindestbreite, bevor es umbricht oder kleiner wird */
            background-color: white;
            padding: 1.5rem; /* Angepasstes Padding */
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .welcome-main h2 {
            color: #343a40;
            margin-bottom: 1rem;
            font-size: 1.6rem; /* Angepasste Größe */
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .welcome-main p, .welcome-main ul {
            font-size: 1rem;
            line-height: 1.6;
            color: #495057;
            margin-bottom: 1.5rem;
        }
        .welcome-main ul {
            padding-left: 20px; /* Einzug für Listen */
        }


        .welcome-sidebar {
            flex-basis: 320px; /* Basisbreite, kann schrumpfen wenn nötig */
            flex-grow: 0; /* Nicht wachsen */
            flex-shrink: 1; /* Kann schrumpfen */
            /* width: 350px; -- Ersetzt durch flex-basis für bessere Responsivität */
            background-color: white;
            padding: 1.5rem; /* Angepasstes Padding */
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .welcome-sidebar h2 {
            color: #343a40;
            margin-bottom: 1rem;
            font-size: 1.4rem; /* Angepasste Größe */
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .public-maps-list {
            list-style: none;
            /* padding: 0; -- Bereits durch globalen Reset abgedeckt */
            /* margin: 0; -- Bereits durch globalen Reset abgedeckt */
        }

        .public-maps-list li {
            padding: 0.8rem 0;
            border-bottom: 1px solid #f0f0f0;
            word-break: break-all; /* Lange Links umbrechen lassen */
        }

        .public-maps-list li:last-child {
            border-bottom: none;
        }

        .public-maps-list a {
            display: block;
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }

        .public-maps-list a:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        .public-maps-list i {
            margin-right: 0.5em;
        }

        .actions {
            margin-top: 2rem;
            text-align: center;
        }

        .actions a {
            display: inline-block;
            padding: 0.8rem 1.5rem; /* Padding angepasst */
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem; /* Angepasste Größe */
            transition: background-color 0.2s, transform 0.1s;
            border: none;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin: 0.5rem; /* Margin für Umbruch auf kleinen Screens */
        }

        .actions a.login-btn {
            background-color: #007bff;
            color: white;
        }

        .actions a.home-btn {
            background-color: #6c757d;
            color: white;
        }

        .actions a:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .actions a.login-btn:hover {
            background-color: #0069d9;
        }

        .actions a.home-btn:hover {
            background-color: #5a6268;
        }

        .welcome-footer {
            text-align: center;
            padding: 2rem 1rem;
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        /* Responsive Anpassungen */
        @media (max-width: 768px) {
            .welcome-content {
                flex-direction: column; /* Haupt- und Seitenbereich untereinander */
            }

            /* .welcome-main und .welcome-sidebar nehmen volle Breite im Column-Layout ein,
               da flex-basis und flex-grow/shrink dies erlauben und kein festes width: 70% mehr gesetzt ist.
               Die Breite wird durch den Parent .welcome-content bestimmt. */
            
            .welcome-header h1 {
                font-size: 1.8rem; /* Noch etwas kleiner für sehr schmale Bildschirme */
            }

            .welcome-header p {
                font-size: 1rem;
                max-width: 100%; /* Sicherstellen, dass es nicht überläuft */
            }
            .actions a {
                padding: 0.7rem 1rem;
                font-size: 0.9rem;
            }

            .simple-content-box {
                padding: 1rem;
            }
        }
         @media (max-width: 480px) {
            .welcome-main, .welcome-sidebar {
                 padding: 1rem; /* Weniger Padding auf sehr kleinen Screens */
            }
             .welcome-main h2 {
                 font-size: 1.4rem;
             }
             .welcome-sidebar h2 {
                 font-size: 1.2rem;
             }
         }
    </style>

    <div class="welcome-header">
        <h1>Willkommen bei Master-Map!</h1>
        <p>Ihre zentrale Plattform zur Visualisierung und Verwaltung Ihrer sportlichen Aktivitäten</p>
    </div>

    <div class="welcome-container">
        <div class="welcome-content">
            <div class="welcome-main">
                <h2>Entdecken Sie Ihre sportlichen Aktivitäten</h2>
<!--
                <p>Master-Map bietet Ihnen eine umfassende Lösung zur Visualisierung und Analyse Ihrer sportlichen Aktivitäten. Verbinden Sie Ihre Accounts von Strava, Google Drive und Komoot, um all Ihre Daten an einem Ort zu haben.</p>

                <h2>Funktionen</h2>
                <ul>
                    <li>Visualisierung Ihrer Aktivitäten auf interaktiven Karten</li>
                    <li>Import von GPX-Dateien aus verschiedenen Quellen</li>
                    <li>Detaillierte Statistiken und Analysen</li>
                    <li>Verwaltung Ihrer Sportausrüstung</li>
                    <li>Teilen von Aktivitäten mit Freunden</li>
                </ul>
-->
                <div class="actions">
                    <a href="/auth/login" class="login-btn">Jetzt einloggen</a>
                </div>
            </div>

            <div class="welcome-sidebar">
                <h2>Öffentliche Karten</h2>
                <% if (locals.publicMapUsers && publicMapUsers.length > 0) { %>
                    <ul class="public-maps-list">
                        <% publicMapUsers.forEach(user => { %>
                            <li>
                                <a href="/show/map/<%= user.username %>" title="Karte von <%= user.username %> anzeigen">
                                    <i class="fas fa-map-marked-alt"></i> <%= user.username %>
                                </a>
                            </li>
                        <% }); %>
                    </ul>
                <% } else { %>
                    <p>Derzeit sind keine öffentlichen Karten verfügbar.</p>
                <% } %>

                <p class="public-map-info">
                    Sie können öffentlich geteilte Karten anderer Benutzer direkt aufrufen,
                    falls Sie einen Link haben (z.B. /show/map/BENUTZERNAME).
                </p>
            </div>
        </div>
    </div>
    <div class="welcome-footer">
        <p>&copy; <%= new Date().getFullYear() %> Master-Map. Alle Rechte vorbehalten.</p>
    </div>