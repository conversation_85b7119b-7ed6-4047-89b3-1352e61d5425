/* public/css/responsive_header.css */

/* Gemeinsame Styles für das responsive Hamburgermenü */

/* Hamburger-Button Styling */
.hamburger-menu-btn {
    display: none; /* Standardmäßig ausgeblendet (nur auf Mobilgeräten sichtbar) */
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
    position: relative;
}

.hamburger-line {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px 0;
    background-color: #333;
    transition: all 0.3s ease;
}

/* Aktiver Zustand des Hamburger-Buttons (wenn Menü geöffnet) */
.hamburger-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* Responsive Styles für Admin-Header */
@media (max-width: 768px) {
    /* Admin Header Anpassungen */
    .admin-main-header {
        position: relative;
    }

    .admin-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
    }

    .admin-main-header h1 {
        margin: 0;
        font-size: 1.5em;
        text-align: left;
    }

    .hamburger-menu-btn {
        display: block; /* Auf Mobilgeräten anzeigen */
    }

    .admin-main-header nav {
        width: 100%;
    }

    .admin-main-header .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        border-bottom: 1px solid #dee2e6;
        padding: 0;
        margin: 0;
        display: none; /* Standardmäßig ausgeblendet */
        flex-direction: column;
        z-index: 1000;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }

    .admin-main-header .nav-menu.active {
        display: flex; /* Wenn aktiv, als Flex anzeigen */
    }

    .admin-main-header .nav-menu li {
        display: block;
        margin: 0;
        border-bottom: 1px solid #e9ecef;
    }

    .admin-main-header .nav-menu li:last-child {
        border-bottom: none;
    }

    .admin-main-header .nav-menu a {
        display: block;
        padding: 12px 15px;
        text-align: left;
    }
}

/* Responsive Styles für User-Header */
@media (max-width: 768px) {
    /* User Header Anpassungen */
    .user-main-header {
        padding: 5px;
    }

    .user-header-content {
        flex-wrap: wrap;
    }

    .user-header-title {
        margin-right: auto;
    }

    .hamburger-menu-btn {
        display: block; /* Auf Mobilgeräten anzeigen */
        order: 3; /* Nach dem Titel */
    }

    .user-main-nav {
        width: 100%;
        order: 4; /* Ganz unten */
    }

    .user-main-nav .nav-menu,
    .user-main-nav #user-nav-menu {
        display: none !important; /* Standardmäßig ausgeblendet mit höherer Priorität */
        flex-direction: column;
        width: 100%;
        padding: 0;
        margin-top: 10px;
    }

    .user-main-nav .nav-menu.active,
    .user-main-nav #user-nav-menu.active {
        display: flex !important; /* Wenn aktiv, als Flex anzeigen mit höherer Priorität */
    }

    .user-main-nav .nav-menu li {
        margin: 0;
        border-bottom: 1px solid #e9ecef;
    }

    .user-main-nav .nav-menu li:last-child {
        border-bottom: none;
    }

    .user-main-nav .nav-menu a {
        /*display: block;*/
        padding: 5px 0;
        /*text-align: left;*/
    }

    /* Anpassungen für das Benachrichtigungs-Dropdown */
    .notifications-dropdown {
        right: 0;
        width: 100%;
        max-width: 320px;
    }
}
