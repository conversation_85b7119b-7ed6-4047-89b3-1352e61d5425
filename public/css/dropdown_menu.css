/* public/css/dropdown_menu.css */

/* === HAMBURGER MENU STYLES (integriert aus responsive_header.css) === */

/* Hamburger-Button Styling */
.hamburger-menu-btn {
    display: none; /* Standardmäßig ausgeblendet (nur auf Mobilgeräten sichtbar) */
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
    position: relative;
}

.hamburger-line {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px 0;
    background-color: #333;
    transition: all 0.3s ease;
}

/* Aktiver Zustand des Hamburger-Buttons (wenn Menü geöffnet) */
.hamburger-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* === DROPDOWN MENU STYLES === */

/* Dropdown-Menü Styling */
.user-main-nav {
    position: relative;
    text-align: right;
}

.user-main-nav .nav-menu {
    display: flex;
    align-items: center;
}

/* Dropdown Container */
.dropdown {
    position: relative;
}

/* Dropdown Toggle Button */
.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 4px;
    transition: background-color 0.2s;
    /* Einheitliche Höhe für alle ersten Menüeinträge */
    min-height: 24px;
    box-sizing: border-box;
    position: relative;
}

/* Hintergrund für Hover-Zustand */
.dropdown-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    border-radius: 4px;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-toggle:hover::before {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 0.7em;
    margin-left: 8px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.dropdown-toggle.active::after {
    transform: rotate(180deg);
}

/* Aktiver Dropdown-Header - Unterstreichung über gesamte Breite */
.dropdown-toggle.active {
    border-bottom: 2px solid #007bff;
    border-radius: 4px 4px 0 0;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none; /* Standardmäßig ausgeblendet */
    min-width: 200px;
    padding: 8px 0;
    margin: 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

/* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
.dropdown-menu.show {
    display: block;
}

.dropdown-menu li {
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
}

.dropdown-menu li a {
    display: block;
    padding: 10px 20px;
    clear: both;
    font-weight: normal;
    text-decoration: none;
    white-space: nowrap;
    transition: background-color 0.2s, color 0.2s;
    /* Einheitliche Höhe für alle Menüeinträge */
    min-height: 20px;
    line-height: 20px;
    box-sizing: border-box;
    position: relative;
    background-color: #f8f9fa; /* Leicht andere Hintergrundfarbe */
    margin-right: 8px; /* Mehr Abstand vom rechten Rand */
}

/* Hintergrund für Hover-Zustand */
.dropdown-menu li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-menu li a:hover {
    color: #16181b;
}

.dropdown-menu li a:hover::before {
    background-color: #e9ecef; /* Dunklerer Hover-Effekt */
}

.dropdown-menu li a:hover {
    background-color: #e9ecef;
}

/* Submenu Styling */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -8px;
    margin-left: -1px;
    display: none; /* Standardmäßig ausgeblendet */
}

.dropdown-submenu .dropdown-menu.show {
    display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
}

.dropdown-submenu .dropdown-toggle {
    /* Stabile Höhe für alle Zustände */
    height: 20px;
    line-height: 20px;
}

.dropdown-submenu .dropdown-toggle::after {
    content: '▼';
    margin-left: 8px;
    transition: transform 0.3s ease;
    transform: rotate(-90deg); /* Startet nach rechts zeigend */
    float: right;
}

.dropdown-submenu .dropdown-toggle.active::after {
    transform: rotate(0deg); /* Zeigt nach unten wenn aktiv */
}

/* Aktiver Submenu-Header - Unterstreichung über gesamte Breite */
.dropdown-submenu .dropdown-toggle.active {
    border-bottom: 1px solid #007bff;
    background-color: #e3f2fd;
}

/* Spezielle Styling für Benachrichtigungen und Logout */
.notifications-item {
    margin-left: auto;
}

.notifications-link {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px !important;
    min-height: 24px !important;
    box-sizing: border-box !important;
    position: relative !important;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notifications-text {
    flex-grow: 1;
}

.notification-badge {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.75em;
    font-weight: bold;
    margin-left: 8px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.logout-item {
    margin-left: 15px;
}

.logout-item a {
    display: flex !important;
    align-items: center;
    padding: 12px 16px !important;
    min-height: 24px !important;
    box-sizing: border-box !important;
    border-radius: 4px;
    transition: background-color 0.2s;
}

/* === RESPONSIVE STYLES (integriert aus responsive_header.css) === */

/* Admin Header Responsive Styles */
@media (max-width: 768px) {
    /* Admin Header Anpassungen */
    .admin-main-header {
        position: relative;
    }

    .admin-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
    }

    .admin-main-header h1 {
        margin: 0;
        font-size: 1.5em;
        text-align: left;
    }

    .hamburger-menu-btn {
        display: block; /* Auf Mobilgeräten anzeigen */
    }

    .admin-main-header nav {
        width: 100%;
    }

    .admin-main-header .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        border-bottom: 1px solid #dee2e6;
        padding: 0;
        margin: 0;
        display: none; /* Standardmäßig ausgeblendet */
        flex-direction: column;
        z-index: 1000;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }

    .admin-main-header .nav-menu.active {
        display: flex; /* Wenn aktiv, als Flex anzeigen */
    }

    .admin-main-header .nav-menu li {
        display: block;
        margin: 0;
        border-bottom: 1px solid #e9ecef;
    }

    .admin-main-header .nav-menu li:last-child {
        border-bottom: none;
    }

    .admin-main-header .nav-menu a {
        display: block;
        padding: 12px 15px;
        text-align: left;
    }

    /* User Header Anpassungen */
    .user-main-header {
        padding: 5px;
    }

    .user-header-content {
        flex-wrap: wrap;
    }

    .user-header-title {
        margin-right: auto;
    }

    .user-main-nav {
        width: 100%;
        order: 4; /* Ganz unten */
    }

    .user-main-nav .nav-menu,
    .user-main-nav #user-nav-menu {
        display: none !important; /* Standardmäßig ausgeblendet mit höherer Priorität */
        flex-direction: column;
        width: 100%;
        padding: 0;
        margin-top: 10px;
        text-align: right; /* Rechtsbündig */
        padding-bottom: 20px; /* Platz für letzten Eintrag */
    }

    .user-main-nav .nav-menu.active,
    .user-main-nav #user-nav-menu.active {
        display: flex !important; /* Wenn aktiv, als Flex anzeigen mit höherer Priorität */
    }

    .user-main-nav .nav-menu li {
        margin: 0;
        border-bottom: 1px solid #e9ecef;
        width: 100%;
    }

    .user-main-nav .nav-menu li:last-child {
        border-bottom: none;
    }

    .dropdown, .notifications-item, .logout-item {
        width: 100%;
        margin: 0; /* Einheitliche Margins */
    }

    .dropdown-toggle, .notifications-link, .logout-item a {
        width: 100%;
        display: flex !important;
        justify-content: flex-end; /* Rechtsbündig */
        align-items: center;
        text-align: right;
        padding: 12px 16px !important; /* Einheitliches Padding */
        min-height: 24px !important; /* Einheitliche Höhe */
        box-sizing: border-box !important;
        margin: 0 !important; /* Keine zusätzlichen Margins */
        border-radius: 4px;
        transition: background-color 0.2s;
        flex-direction: row-reverse; /* Pfeil links, Text rechts */
    }

    .dropdown-toggle::after {
        content: '▼';
        font-size: 0.7em;
        margin-right: 8px; /* Abstand zum Text */
        margin-left: 0;
        transition: transform 0.3s ease;
        flex-shrink: 0;
        order: -1; /* Pfeil vor dem Text */
    }

    .dropdown-toggle.active::after {
        transform: rotate(180deg);
    }

    .dropdown-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        border: none;
        background-color: #f1f3f4;
        padding-right: 20px;
        display: none; /* Standardmäßig ausgeblendet */
    }

    .dropdown-menu li a {
        margin-right: 12px; /* Mehr Abstand in mobiler Ansicht */
        background-color: #f1f3f4;
        text-align: right; /* Text rechtsbündig */
        direction: ltr; /* Text wieder normal ausrichten */
    }

    .dropdown-menu.show {
        display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
    }

    .dropdown-submenu .dropdown-menu {
        margin-right: 20px;
        padding-left: 16px;
        background-color: #e8eaed; /* Noch dunklere Farbe für Submenüs */
        display: none; /* Standardmäßig ausgeblendet */
        position: static; /* Verhindert Überlagerung */
        width: calc(100% - 20px); /* Breite anpassen */
    }

    .dropdown-submenu .dropdown-menu li a {
        background-color: #e8eaed;
        margin-right: 12px; /* Abstand für Submenü-Einträge */
        text-align: right; /* Text rechtsbündig */
        padding: 8px 16px; /* Kleineres Padding für Submenü */
    }

    .dropdown-submenu .dropdown-toggle {
        text-align: right; /* Text rechtsbündig */
        background-color: #f1f3f4; /* Gleiche Farbe wie Hauptmenü */
        padding: 10px 16px !important; /* Etwas kleineres Padding */
        display: flex !important;
        justify-content: flex-end;
        align-items: center;
        flex-direction: row-reverse; /* Pfeil links, Text rechts */
    }

    .dropdown-submenu .dropdown-toggle::after {
        content: '▼';
        margin-right: 8px; /* Abstand zum Text */
        margin-left: 0;
        float: none; /* Float entfernen */
        transition: transform 0.3s ease;
        transform: rotate(-90deg); /* Startet nach rechts zeigend */
        order: -1; /* Pfeil vor dem Text */
    }

    .dropdown-submenu .dropdown-toggle.active::after {
        transform: rotate(0deg); /* Zeigt nach unten wenn aktiv */
    }

    .dropdown-submenu .dropdown-menu.show {
        display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
    }

    /* Entfernt - bereits oben definiert */

    .notifications-item, .logout-item {
        margin-left: 0;
        width: 100%;
    }

    .notifications-link, .logout-item a {
        width: 100% !important;
        justify-content: flex-end !important; /* Rechtsbündig */
        text-align: right !important;
        direction: ltr !important;
    }

    .notifications-text {
        text-align: right;
        flex-grow: 1;
    }

    .notification-badge {
        margin-left: 8px;
        margin-right: 0;
    }

    /* Anpassungen für das Benachrichtigungs-Dropdown */
    .notifications-dropdown {
        right: 0;
        width: 100%;
        max-width: 320px;
    }
}
