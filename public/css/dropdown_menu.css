/* public/css/dropdown_menu.css */

/* Dropdown-<PERSON><PERSON> */
.user-main-nav {
    position: relative;
    text-align: right;
}

.user-main-nav .nav-menu {
    display: flex;
    align-items: center;
}

/* Dropdown Container */
.dropdown {
    position: relative;
}

/* Dropdown Toggle Button */
.dropdown-toggle {
    /*display: flex;*/
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.2s;
    /* Stabile Höhe für alle Zustände */
    height: 20px;
    box-sizing: content-box;
}

/* Hintergrund für Hover-Zustand */
.dropdown-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    border-radius: 4px;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-toggle:hover::before {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 0.6em;
    margin-left: 6px;
    transition: transform 0.2s;
}

.dropdown-toggle.active::after {
    transform: rotate(180deg);
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none; /* Standardmäßig ausgeblendet */
    min-width: 200px;
    padding: 8px 0;
    margin: 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

/* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
.dropdown-menu.show {
    display: block;
}

.dropdown-menu li {
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
}

.dropdown-menu li a {
    display: block;
    padding: 8px 16px;
    clear: both;
    font-weight: normal;
    text-decoration: none;
    white-space: nowrap;
    transition: background-color 0.2s, color 0.2s;
    /* Stabile Höhe für alle Zustände */
    height: 20px;
    line-height: 20px;
    box-sizing: content-box;
    position: relative;
}

/* Hintergrund für Hover-Zustand */
.dropdown-menu li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-menu li a:hover {
    color: #16181b;
}

.dropdown-menu li a:hover::before {
    background-color: #f8f9fa;
}

/* Submenu Styling */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -8px;
    margin-left: -1px;
    display: none; /* Standardmäßig ausgeblendet */
}

.dropdown-submenu .dropdown-menu.show {
    display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
}

.dropdown-submenu .dropdown-toggle {
    /* Stabile Höhe für alle Zustände */
    height: 20px;
    line-height: 20px;
}

.dropdown-submenu .dropdown-toggle::after {
    content: '▶';
    float: right;
    margin-left: 8px;
    transition: transform 0.2s;
}

.dropdown-submenu .dropdown-toggle.active::after {
    transform: rotate(90deg);
}

/* Spezielle Styling für Benachrichtigungen und Logout */
.notifications-icon {
    margin-left: auto;
}

.logout-item {
    margin-left: 15px;
}

/* Mobile Anpassungen */
@media (max-width: 768px) {
    .user-main-nav .nav-menu {
        flex-direction: column;
/*        align-items: flex-start;*/
        align-items: stretch;
    }

    .dropdown {
        width: 100%;
    }

    .dropdown-toggle {
        width: 100%;
        justify-content: space-between;
    }

    .dropdown-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        border: none;
        background-color: #f8f9fa;
        padding-right: 25px;
        display: none; /* Standardmäßig ausgeblendet */
    }

    .dropdown-menu.show {
        display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
    }

    .dropdown-submenu .dropdown-menu {
        margin-right: 20px;
        padding-left: 15px;
        display: none; /* Standardmäßig ausgeblendet */
    }

    .dropdown-submenu .dropdown-menu.show {
        display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
    }

    .dropdown-submenu .dropdown-toggle::after {
        transform: rotate(90deg);
    }

    .dropdown-submenu .dropdown-toggle.active::after {
        transform: rotate(180deg);
    }

    .notifications-icon, .logout-item {
        margin-left: 0;
        width: 100%;
    }
}
