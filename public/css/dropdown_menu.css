/* public/css/dropdown_menu.css */
/* <PERSON><PERSON><PERSON>, konsolidierte CSS ohne !important Regeln */

/* === HAMBURGER MENU STYLES === */
.hamburger-menu-btn {
    display: none;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
    position: relative;
}

.hamburger-line {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px 0;
    background-color: #333;
    transition: all 0.3s ease;
}

.hamburger-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.hamburger-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* === DESKTOP DROPDOWN STYLES === */
.user-main-nav {
    position: relative;
    text-align: right;
}

.user-main-nav .nav-menu {
    display: flex;
    align-items: center;
    list-style: none; /* <PERSON><PERSON><PERSON><PERSON>, dass es keine Listenpunkte gibt */
    padding: 0;
    margin: 0;    
}

.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 4px;
    transition: background-color 0.2s;
    min-height: 24px;
    box-sizing: border-box;
    position: relative;
}

.dropdown-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    border-radius: 4px;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-toggle:hover::before {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 0.7em;
    margin-left: 8px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.dropdown-toggle.active::after {
    transform: rotate(180deg);
}

.dropdown-toggle.active {
    border-bottom: 2px solid #007bff;
    border-radius: 4px 4px 0 0;
}

/* === DROPDOWN MENU === */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    min-width: 200px;
    padding: 8px 0;
    margin: 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu li {
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
}

.dropdown-menu li a {
    display: block;
    padding: 10px 20px;
    clear: both;
    font-weight: normal;
    text-decoration: none;
    white-space: nowrap;
    transition: background-color 0.2s, color 0.2s;
    min-height: 20px;
    line-height: 20px;
    box-sizing: border-box;
    position: relative;
    background-color: #f8f9fa;
    margin-right: 8px;
}

.dropdown-menu li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-menu li a:hover {
    color: #16181b;
    background-color: #e9ecef;
}

.dropdown-menu li a:hover::before {
    background-color: #e9ecef;
}

/* === SUBMENU STYLES === */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -8px;
    margin-left: -1px;
    display: none;
}

.dropdown-submenu .dropdown-menu.show {
    display: block;
}

.dropdown-submenu .dropdown-toggle {
    min-height: 20px;
    line-height: 20px;
}

.dropdown-submenu .dropdown-toggle::after {
    content: '▼';
    margin-left: 8px;
    transition: transform 0.3s ease;
    transform: rotate(-90deg);
    float: right;
}

.dropdown-submenu .dropdown-toggle.active::after {
    transform: rotate(0deg);
}

.dropdown-submenu .dropdown-toggle.active {
    border-bottom: 1px solid #007bff;
    background-color: #e3f2fd;
}

/* === NOTIFICATIONS AND LOGOUT === */
.notifications-item {
    margin-left: auto;
}

.notifications-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    min-height: 24px;
    box-sizing: border-box;
    position: relative;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notifications-text {
    flex-grow: 1;
}

.notification-badge {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.75em;
    font-weight: bold;
    margin-left: 8px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.logout-item {
    margin-left: 15px;
}

.logout-item a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    min-height: 24px;
    box-sizing: border-box;
    border-radius: 4px;
    transition: background-color 0.2s;
}

/* === ADMIN RESPONSIVE STYLES === */
@media (max-width: 768px) {
    .admin-main-header {
        position: relative;
    }

    .admin-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
    }

    .admin-main-header h1 {
        margin: 0;
        font-size: 1.5em;
        text-align: left;
    }

    .hamburger-menu-btn {
        display: block;
    }

    .admin-main-header nav {
        width: 100%;
    }

    .admin-main-header .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        border-bottom: 1px solid #dee2e6;
        padding: 0;
        margin: 0;
        display: none;
        flex-direction: column;
        z-index: 1000;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
    }

    .admin-main-header .nav-menu.active {
        display: flex;
    }

    .admin-main-header .nav-menu li {
        display: block;
        margin: 0;
        border-bottom: 1px solid #e9ecef;
    }

    .admin-main-header .nav-menu li:last-child {
        border-bottom: none;
    }

    .admin-main-header .nav-menu a {
        display: block;
        padding: 12px 15px;
        text-align: left;
    }

    /* === USER MOBILE STYLES === */
    .user-main-header {
        padding: 5px;
    }

    .user-header-content {
        flex-wrap: wrap;
    }

    .user-header-title {
        margin-right: auto;
    }

    .user-main-nav {
        width: 100%;
        order: 4;
    }

    /* Override user_layout.css styles with more specific selectors */
    .user-main-nav .nav-menu,
    .user-main-nav #user-nav-menu {
        display: none;
        flex-direction: column;
        width: 100%;
        padding: 0;
        margin-top: 10px;
        padding-bottom: 20px;
        gap: 0;
    }

    .user-main-nav .nav-menu.active,
    .user-main-nav #user-nav-menu.active {
        display: flex;
    }

    .user-main-nav .nav-menu li {
        margin: 0;
        border-bottom: 1px solid #e9ecef;
        width: 100%;
    }

    .user-main-nav .nav-menu li:last-child {
        border-bottom: none;
    }

    /* === MOBILE ERSTE EBENE === */
    .user-main-nav .nav-menu .dropdown,
    .user-main-nav .nav-menu .notifications-item,
    .user-main-nav .nav-menu .logout-item {
        width: 100%;
        margin: 0;
    }

    .user-main-nav .nav-menu .dropdown .dropdown-toggle,
    .user-main-nav .nav-menu .notifications-item .notifications-link,
    .user-main-nav .nav-menu .logout-item a {
        width: 100%;
        display: block;
        text-align: right;
        padding: 12px 16px;
        min-height: 24px;
        box-sizing: border-box;
        margin: 0;
        border-radius: 4px;
        transition: background-color 0.2s;
        position: relative;
        background-color: transparent;
    }

    /* Mobile Pfeile für erste Ebene */
    .user-main-nav .nav-menu .dropdown .dropdown-toggle::after {
        content: '▼';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 0.7em;
        transition: transform 0.3s ease;
        margin-left: 0;
        float: none;
    }

    .user-main-nav .nav-menu .dropdown .dropdown-toggle.active::after {
        transform: translateY(-50%) rotate(180deg);
    }

    /* === MOBILE DROPDOWN MENUS === */
    .user-main-nav .dropdown-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        border: none;
        background-color: #f1f3f4;
        padding-right: 20px;
        display: none;
    }

    .user-main-nav .dropdown-menu.show {
        display: block;
    }

    .user-main-nav .dropdown-menu li a {
        margin-right: 12px;
        background-color: #f1f3f4;
        text-align: right;
    }

    /* === MOBILE SUBMENUS === */
    .user-main-nav .dropdown-submenu .dropdown-menu {
        margin-right: 20px;
        padding-left: 16px;
        background-color: #e8eaed;
        display: none;
        position: static;
        width: calc(100% - 20px);
        margin-top: 0;
    }

    .user-main-nav .dropdown-submenu .dropdown-menu li a {
        background-color: #e8eaed;
        margin-right: 12px;
        text-align: right;
        padding: 8px 16px;
    }

    .user-main-nav .dropdown-submenu .dropdown-toggle {
        background-color: #f1f3f4;
        padding: 10px 16px;
        text-align: right;
        display: block;
        width: 100%;
        position: relative;
        margin-right: 8px;
    }

    .user-main-nav .dropdown-submenu .dropdown-toggle::after {
        content: '▼';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%) rotate(-90deg);
        font-size: 0.7em;
        transition: transform 0.3s ease;
        float: none;
        margin: 0;
    }

    .user-main-nav .dropdown-submenu .dropdown-toggle.active::after {
        transform: translateY(-50%) rotate(0deg);
    }

    .user-main-nav .dropdown-submenu .dropdown-menu.show {
        display: block;
    }

    /* === MOBILE NOTIFICATIONS === */
    .user-main-nav .notifications-text {
        text-align: right;
        flex-grow: 1;
    }

    .user-main-nav .notification-badge {
        margin-left: 8px;
        margin-right: 0;
    }

    .user-main-nav .notifications-dropdown {
        right: 0;
        width: 100%;
        max-width: 320px;
    }
}
