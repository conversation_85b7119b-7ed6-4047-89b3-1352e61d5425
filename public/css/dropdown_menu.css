/* public/css/dropdown_menu.css */

/* Dropdown-<PERSON><PERSON> */
.user-main-nav {
    position: relative;
    text-align: right;
}

.user-main-nav .nav-menu {
    display: flex;
    align-items: center;
}

/* Dropdown Container */
.dropdown {
    position: relative;
}

/* Dropdown Toggle Button */
.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 4px;
    transition: background-color 0.2s;
    /* Einheitliche Höhe für alle ersten Menüeinträge */
    min-height: 24px;
    box-sizing: border-box;
    position: relative;
}

/* Hintergrund für Hover-Zustand */
.dropdown-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    border-radius: 4px;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-toggle:hover::before {
    background-color: rgba(0, 0, 0, 0.05);
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 0.7em;
    margin-left: 8px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.dropdown-toggle.active::after {
    transform: rotate(180deg);
}

/* Aktiver Dropdown-Header - Unterstreichung über gesamte Breite */
.dropdown-toggle.active {
    border-bottom: 2px solid #007bff;
    border-radius: 4px 4px 0 0;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none; /* Standardmäßig ausgeblendet */
    min-width: 200px;
    padding: 8px 0;
    margin: 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

/* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
.dropdown-menu.show {
    display: block;
}

.dropdown-menu li {
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
}

.dropdown-menu li a {
    display: block;
    padding: 10px 20px;
    clear: both;
    font-weight: normal;
    text-decoration: none;
    white-space: nowrap;
    transition: background-color 0.2s, color 0.2s;
    /* Einheitliche Höhe für alle Menüeinträge */
    min-height: 20px;
    line-height: 20px;
    box-sizing: border-box;
    position: relative;
    background-color: #f8f9fa; /* Leicht andere Hintergrundfarbe */
    margin-right: 8px; /* Mehr Abstand vom rechten Rand */
}

/* Hintergrund für Hover-Zustand */
.dropdown-menu li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent;
    transition: background-color 0.2s;
    z-index: -1;
}

.dropdown-menu li a:hover {
    color: #16181b;
}

.dropdown-menu li a:hover::before {
    background-color: #e9ecef; /* Dunklerer Hover-Effekt */
}

.dropdown-menu li a:hover {
    background-color: #e9ecef;
}

/* Submenu Styling */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -8px;
    margin-left: -1px;
    display: none; /* Standardmäßig ausgeblendet */
}

.dropdown-submenu .dropdown-menu.show {
    display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
}

.dropdown-submenu .dropdown-toggle {
    /* Stabile Höhe für alle Zustände */
    height: 20px;
    line-height: 20px;
}

.dropdown-submenu .dropdown-toggle::after {
    content: '▶';
    float: right;
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.dropdown-submenu .dropdown-toggle.active::after {
    transform: rotate(180deg); /* 180 Grad statt 90 Grad */
}

/* Aktiver Submenu-Header - Unterstreichung über gesamte Breite */
.dropdown-submenu .dropdown-toggle.active {
    border-bottom: 1px solid #007bff;
    background-color: #e3f2fd;
}

/* Spezielle Styling für Benachrichtigungen und Logout */
.notifications-item {
    margin-left: auto;
}

.notifications-link {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px !important;
    min-height: 24px !important;
    box-sizing: border-box !important;
    position: relative !important;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notifications-text {
    flex-grow: 1;
}

.notification-badge {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.75em;
    font-weight: bold;
    margin-left: 8px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.logout-item {
    margin-left: 15px;
}

.logout-item a {
    display: flex !important;
    align-items: center;
    padding: 12px 16px !important;
    min-height: 24px !important;
    box-sizing: border-box !important;
    border-radius: 4px;
    transition: background-color 0.2s;
}

/* Mobile Anpassungen */
@media (max-width: 768px) {
    .user-main-nav .nav-menu {
        flex-direction: column;
/*        align-items: flex-start;*/
        align-items: stretch;
    }

    .dropdown {
        width: 100%;
    }

    .dropdown-toggle {
        width: 100%;
        justify-content: space-between;
    }

    .dropdown-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        border: none;
        background-color: #f1f3f4;
        padding-right: 20px;
        display: none; /* Standardmäßig ausgeblendet */
    }

    .dropdown-menu li a {
        margin-right: 12px; /* Mehr Abstand in mobiler Ansicht */
        background-color: #f1f3f4;
    }

    .dropdown-menu.show {
        display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
    }

    .dropdown-submenu .dropdown-menu {
        margin-right: 24px;
        padding-left: 20px;
        background-color: #e8eaed; /* Noch dunklere Farbe für Submenüs */
        display: none; /* Standardmäßig ausgeblendet */
    }

    .dropdown-submenu .dropdown-menu li a {
        background-color: #e8eaed;
        margin-right: 16px; /* Noch mehr Abstand für Submenü-Einträge */
    }

    .dropdown-submenu .dropdown-menu.show {
        display: block; /* Nur anzeigen, wenn die Klasse 'show' vorhanden ist */
    }

    .dropdown-submenu .dropdown-toggle::after {
        transform: rotate(90deg);
    }

    .dropdown-submenu .dropdown-toggle.active::after {
        transform: rotate(270deg); /* 270 Grad für mobile Ansicht */
    }

    .notifications-item, .logout-item {
        margin-left: 0;
        width: 100%;
    }

    .notifications-link, .logout-item a {
        width: 100% !important;
        justify-content: flex-start !important;
    }
}
