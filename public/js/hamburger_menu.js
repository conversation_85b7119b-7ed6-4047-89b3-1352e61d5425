// public/js/hamburger_menu.js

/**
 * Hamburger-Menü Funktionalität für responsive Navigation
 * Wird sowohl für Admin- als auch User-Header verwendet
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('Hamburger menu script loaded');

    // Hamburger-Button und Navigationsmenü finden
    const hamburgerBtn = document.querySelector('.hamburger-menu-btn');

    // Versuche zuerst die ID zu finden, dann die Klasse als Fallback
    const navMenu = document.getElementById('user-nav-menu') ||
                    document.getElementById('admin-nav-menu') ||
                    document.querySelector('.nav-menu');

    // Nur fortfahren, wenn beide Elemente existieren
    if (!hamburgerBtn || !navMenu) {
        console.warn('Hamburger menu elements not found:', {
            hamburgerBtn: hamburgerBtn ? 'found' : 'not found',
            navMenu: navMenu ? 'found' : 'not found',
            navMenuId: navMenu ? navMenu.id : 'N/A'
        });
        return;
    }


    // Toggle-Funktion für das Menü
    function toggleMenu() {

        // Nur in der mobilen Ansicht umschalten
        if (window.innerWidth <= 768) {
            navMenu.classList.toggle('active');
            hamburgerBtn.classList.toggle('active');

            // Aria-expanded Attribut für Barrierefreiheit aktualisieren
            const isExpanded = navMenu.classList.contains('active');
            hamburgerBtn.setAttribute('aria-expanded', isExpanded);
        } else { 
            if (navMenu.classList.contains('active')) { 
                navMenu.classList.remove('active');
                hamburgerBtn.classList.remove('active'); 
                hamburgerBtn.setAttribute('aria-expanded', 'false'); 
            }
            console.log('Toggle menu called in desktop view, adjusted state.'); 
        }
    }

    // Event-Listener für den Hamburger-Button
    hamburgerBtn.addEventListener('click', (event) => {
        event.preventDefault();
        toggleMenu();
    });

    // Menü schließen, wenn außerhalb geklickt wird
    document.addEventListener('click', (event) => {
        if (navMenu.classList.contains('active') &&
            !navMenu.contains(event.target) &&
            !hamburgerBtn.contains(event.target)) {
            toggleMenu();
        }
    });

    // Menü schließen, wenn Escape-Taste gedrückt wird
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && navMenu.classList.contains('active')) {
            toggleMenu();
        }
    });

    // Menü schließen, wenn Bildschirmgröße ändert und Desktop-Ansicht erreicht wird
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768 && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            hamburgerBtn.classList.remove('active');
            hamburgerBtn.setAttribute('aria-expanded', 'false');
        }
    });

    // Initialisierung: Stelle sicher, dass das Menü beim Laden korrekt angezeigt wird
    function initializeMenu() {
        console.log('Initializing menu state');
        // Entferne die active-Klasse, falls sie vorhanden ist
        navMenu.classList.remove('active');
        hamburgerBtn.classList.remove('active');
        hamburgerBtn.setAttribute('aria-expanded', 'false');
    }

    // Initialisierung ausführen
    initializeMenu();

});
