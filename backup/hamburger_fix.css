/* public/css/hamburger_fix.css */
/* Spezielle CSS-Datei zur Behebung des Hamburger-Menü-Problems */

/* DESKTOP-ANSICHT: <PERSON><PERSON> sic<PERSON>, dass das Menü immer angezeigt wird */
@media (min-width: 769px) {
    /* Menü immer anzeigen */
    .user-main-nav .nav-menu,
    .user-main-nav #user-nav-menu,
    #user-nav-menu {
        display: flex !important;
    }

    /* Hamburger-Button ausblenden */
    .hamburger-menu-btn {
        display: none !important;
    }
}

/* MOBILE-ANSICHT: Menü standardmäßig ausblenden, nur bei active anzeigen */
@media (max-width: 768px) {
    /* Menü standardmäßig ausblenden */
    .user-main-nav .nav-menu,
    .user-main-nav #user-nav-menu,
    #user-nav-menu {
        display: none !important;
    }

    /* Menü nur anzeigen, wenn active-Klasse vorhanden ist */
    .user-main-nav .nav-menu.active,
    .user-main-nav #user-nav-menu.active,
    #user-nav-menu.active {
        display: flex !important;
    }

    /* Hamburger-Button immer anzeigen */
    .hamburger-menu-btn {
        display: block !important;
    }
}

/* Überschreibe alle anderen display-Stile für das Menü */
.user-main-nav .nav-menu[style*="display: none"],
.user-main-nav #user-nav-menu[style*="display: none"],
#user-nav-menu[style*="display: none"] {
    display: flex !important;
}
