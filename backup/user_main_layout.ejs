<%# views/layouts/user_main_layout.ejs %>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= locals.pageTitle || 'Meine Seite' %> - <%= locals.config.app.name || 'Master-Map' %></title>

    <%# Globale CSS-Datei für den User-Bereich %>
    <link rel="stylesheet" href="/css/user_layout.css">

    <%# Responsive Header Styles für Hamburgermenü %>
    <link rel="stylesheet" href="/css/responsive_header.css">

    <%# Fix für das Hamburger-Menü-Problem %>
    <link rel="stylesheet" href="/css/hamburger_fix.css">

    <%# Platzhalter für seitenspezifische Stylesheets, die von den einzelnen Views befüllt werden können %>
    <%# Diese werden im <head> des gerenderten HTMLs erscheinen %>
    <%- defineContent('pageStylesheets') %>
</head>
<body class="user-page <%= typeof pageSpecificClass !== 'undefined' ? pageSpecificClass : '' %>">

    <%# Der gemeinsame User-Header %>
    <%# Pfad relativ zum 'views'-Ordner, da Layouts oft direkt im views-Ordner oder einem Unterordner davon liegen %>
    <%# Wenn user_header.ejs in views/users/partials/ liegt: %>
    <%- include('../users/partials/user_header') %>

    <main class="user-content-wrapper">
        <div class="user-content-inner">
            <%# Globale Nachrichten-Boxen, die Daten aus res.locals verwenden %>
            <% if (locals.successMessage && locals.successMessage.length > 0) { %>
                <div class="message success-message">
                    <% successMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>
            <% if (locals.errorMessage && locals.errorMessage.length > 0) { %>
                <div class="message error-message">
                    <% errorMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>
            <% if (locals.infoMessage && locals.infoMessage.length > 0) { %>
                <div class="message info-message">
                    <% infoMessage.forEach(function(msg) { %>
                        <p><%- msg %></p>
                    <% }); %>
                </div>
            <% } %>

            <%- body %> <%# Hier wird der Inhalt der spezifischen View (z.B. dashboard.ejs) von express-ejs-layouts injiziert %>
        </div>
    </main>

    <footer class="user-main-footer">
        <p>&copy; <%= locals.currentYear || new Date().getFullYear() %> <%= locals.config.app.name || 'Master-Map' %>. Alle Rechte vorbehalten.</p>
    </footer>

    <%# Globale JavaScript-Dateien, die am Ende des Bodys geladen werden sollen (falls vorhanden) %>
    <%# z.B. <script src="/js/global_ui_helpers.js"></script> %>
    <script src="/js/notification_handler.js" defer></script> <%# Für das Benachrichtigungs-Dropdown im Header %>
    <script src="/js/hamburger_menu.js" defer></script> <%# Für das responsive Hamburgermenü %>


    <%# Platzhalter für seitenspezifische Skripte, die von den Views befüllt werden können %>
    <%# Diese werden am Ende des <body> im gerenderten HTMLs erscheinen %>
    <%- defineContent('pageScripts') %>
</body>
</html>
